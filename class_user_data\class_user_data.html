<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>班级学生访问信息</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        .card {
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .card-header {
            background-color: #6c757d;
            color: white;
            font-weight: bold;
        }
        .nav-tabs .nav-link.active {
            font-weight: bold;
            background-color: #f8f9fa;
        }
        .tab-content {
            padding: 15px;
            background-color: white;
            border-left: 1px solid #dee2e6;
            border-right: 1px solid #dee2e6;
            border-bottom: 1px solid #dee2e6;
            border-radius: 0 0 4px 4px;
            margin-top: -1px;
        }
        .student-details .tab-content {
            max-height: 400px;
            overflow-y: auto;
        }
        .nav-tabs .nav-link {
            cursor: pointer;
        }
        .student-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .student-list-item {
            padding: 10px 15px;
            margin-bottom: 5px;
            background-color: #fff;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
            border-left: 3px solid transparent;
        }
        .student-list-item:hover {
            background-color: #f0f0f0;
            transform: translateY(-1px);
            box-shadow: 0 2px 5px rgba(0,0,0,0.15);
            border-left: 3px solid #0d6efd;
        }
        .student-info {
            display: flex;
            align-items: center;
            flex: 1;
        }
        .student-name {
            font-weight: 500;
            margin-right: 10px;
        }
        .student-details {
            margin-top: 10px;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            width: 100%;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .no-records {
            color: #6c757d;
            font-style: italic;
            font-size: 0.9rem;
            margin-left: 10px;
        }
        .has-records {
            color: #198754;
        }
        .badges-container {
            display: flex;
            gap: 5px;
            margin-left: 10px;
        }
        .chat-item, .video-item {
            padding: 8px;
            margin-bottom: 10px;
            border-radius: 4px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .chat-item {
            background-color: #e9f5ff;
            border-left: 3px solid #0d6efd;
        }
        .video-item {
            background-color: #f0f8e6;
            border-left: 3px solid #198754;
        }
        .timestamp {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .student-indicator {
            display: inline-flex;
            justify-content: center;
            align-items: center;
            width: 24px;
            height: 24px;
            color: #6c757d;
            transition: transform 0.2s;
        }
        .student-list-item.active {
            background-color: #e9f5ff;
            border-left: 3px solid #0d6efd;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .bi-arrow-repeat.spin {
            animation: spin 1s linear infinite;
        }
        
        .alert {
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="mb-4">班级学生访问信息</h1>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>选择班级</span>
                            <button id="syncButton" class="btn btn-sm btn-primary">
                                <i class="bi bi-arrow-repeat"></i> 同步用户数据
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="loadingIndicator" class="text-center">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p>正在加载班级数据...</p>
                        </div>
                        <div id="noDataMessage" class="alert alert-warning" style="display: none;"></div>
                        <select id="classSelector" class="form-select mb-3" style="display: none;">
                            <option value="">-- 请选择班级 --</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        
        <div id="studentDataContainer" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <span id="classTitle">班级学生列表</span>
                        <span id="studentCount" class="badge bg-primary">0 名学生</span>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 添加选项卡导航 -->
                    <ul class="nav nav-tabs mb-3" id="classDataTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="students-tab" data-bs-toggle="tab" 
                                    data-bs-target="#students-content" type="button" role="tab" 
                                    aria-controls="students-content" aria-selected="true">
                                学生列表
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="summary-tab" data-bs-toggle="tab" 
                                    data-bs-target="#summary-content" type="button" role="tab" 
                                    aria-controls="summary-content" aria-selected="false">
                                班级汇总
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="homework-tab" data-bs-toggle="tab" 
                                    data-bs-target="#homework-content" type="button" role="tab" 
                                    aria-controls="homework-content" aria-selected="false">
                                作业统计
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="regular-grade-tab" data-bs-toggle="tab"
                                    data-bs-target="#regular-grade-content" type="button" role="tab"
                                    aria-controls="regular-grade-content" aria-selected="false">
                                平时成绩
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="questions-tab" data-bs-toggle="tab"
                                    data-bs-target="#questions-content" type="button" role="tab"
                                    aria-controls="questions-content" aria-selected="false">
                                问题统计
                            </button>
                        </li>
                    </ul>
                    
                    <!-- 选项卡内容 -->
                    <div class="tab-content" id="classDataTabsContent">
                        <!-- 学生列表选项卡 -->
                        <div class="tab-pane fade show active" id="students-content" role="tabpanel" aria-labelledby="students-tab">
                            <ul id="studentList" class="student-list"></ul>
                        </div>
                        
                        <!-- 班级汇总选项卡 -->
                        <div class="tab-pane fade" id="summary-content" role="tabpanel" aria-labelledby="summary-tab">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-primary text-white">
                                            提问记录统计
                                        </div>
                                        <div class="card-body">
                                            <div id="chatSummary">
                                                <div class="text-center">
                                                    <div class="spinner-border text-primary" role="status">
                                                        <span class="visually-hidden">加载中...</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-header bg-success text-white">
                                            视频观看统计
                                        </div>
                                        <div class="card-body">
                                            <div id="videoSummary">
                                                <div class="text-center">
                                                    <div class="spinner-border text-success" role="status">
                                                        <span class="visually-hidden">加载中...</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card">
                                <div class="card-header">
                                    活跃度排名
                                </div>
                                <div class="card-body">
                                    <div id="activityRanking">
                                        <div class="text-center">
                                            <div class="spinner-border text-primary" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 作业统计选项卡 -->
                        <div class="tab-pane fade" id="homework-content" role="tabpanel" aria-labelledby="homework-tab">
                            <div class="card mb-3">
                                <div class="card-header bg-warning text-white">
                                    作业统计
                                </div>
                                <div class="card-body">
                                    <div id="homeworkSummary">
                                        <div class="text-center">
                                            <div class="spinner-border text-warning" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 平时成绩选项卡 -->
                        <div class="tab-pane fade" id="regular-grade-content" role="tabpanel" aria-labelledby="regular-grade-tab">
                            <!-- 计算参数设置卡片 -->
                            <div class="card mb-3">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="bi bi-gear-fill"></i> 平时成绩计算参数设置
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-4">
                                            <label for="homeworkWeightSlider" class="form-label">
                                                <strong>作业权重系数：</strong>
                                                <span id="homeworkWeightValue" class="badge bg-primary">0.5</span>
                                            </label>
                                            <input type="range" class="form-range" id="homeworkWeightSlider"
                                                   min="0" max="1" step="0.1" value="0.5">
                                            <div class="form-text">调整作业平均分在平时成绩中的权重（0-1之间）</div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="alert alert-light border mb-0">
                                                <i class="bi bi-calculator"></i>
                                                <strong>当前计算公式：</strong><br>
                                                <span id="formulaDisplay">
                                                    平时成绩 = 作业平均分 × <span class="text-primary fw-bold">0.5</span> + 提问记录分（最高25分）+ 视频观看分（最高25分）
                                                </span>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <button id="recalculateBtn" class="btn btn-success btn-sm w-100">
                                                <i class="bi bi-arrow-clockwise"></i> 重新计算
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 平时成绩统计结果卡片 -->
                            <div class="card mb-3">
                                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                                    <span>
                                        <i class="bi bi-bar-chart-fill"></i> 平时成绩统计结果
                                    </span>
                                    <button id="downloadRegularGradeBtn" class="btn btn-light btn-sm">
                                        <i class="bi bi-download"></i> 下载CSV
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="regularGradeSummary">
                                        <div class="text-center">
                                            <div class="spinner-border text-info" role="status">
                                                <span class="visually-hidden">加载中...</span>
                                            </div>
                                            <p class="mt-2 text-muted">请先选择班级并设置计算参数</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 问题统计选项卡 -->
                        <div class="tab-pane fade" id="questions-content" role="tabpanel" aria-labelledby="questions-tab">
                            <div class="card mb-4">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <span>常见问题统计</span>
                                    <button id="generateQuestionsBtn" class="btn btn-primary">
                                        <i class="bi bi-lightning"></i> 生成问题统计
                                    </button>
                                </div>
                                <div class="card-body">
                                    <div id="questionsLoadingIndicator" style="display: none;" class="text-center my-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">处理中...</span>
                                        </div>
                                        <p>正在分析学生问题，这可能需要一些时间...</p>
                                    </div>
                                    <div id="questionsErrorMessage" style="display: none;" class="alert alert-danger"></div>
                                    <div id="questionsContent">
                                        <div class="alert alert-info">
                                            <i class="bi bi-info-circle"></i> 点击"生成问题统计"按钮，分析该班级学生提出的共性问题。
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 获取URL参数
        function getUrlParams() {
            const params = {};
            const queryString = window.location.search.substring(1);
            const pairs = queryString.split('&');
            
            for (const pair of pairs) {
                const [key, value] = pair.split('=');
                if (key && value) {
                    params[key] = decodeURIComponent(value);
                }
            }
            
            return params;
        }
        
        // HTML转义函数
        function escapeHtml(unsafe) {
            return unsafe
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }

        // 渲染平时成绩统计 - 全局函数
        function renderRegularGradeSummary(students) {
            console.log('renderRegularGradeSummary called with students:', students);
            console.log('学生数量:', students ? students.length : 0);

            // 获取当前作业权重设置
            const homeworkWeightSlider = document.getElementById('homeworkWeightSlider');
            console.log('homeworkWeightSlider element:', homeworkWeightSlider);

            const homeworkWeight = homeworkWeightSlider ? parseFloat(homeworkWeightSlider.value) : 0.5;
            console.log('当前作业权重:', homeworkWeight);

            // 检查目标容器是否存在
            const targetContainer = document.getElementById('regularGradeSummary');
            console.log('regularGradeSummary container:', targetContainer);

            if (!targetContainer) {
                console.error('找不到 regularGradeSummary 容器');
                return;
            }

            if (!students || students.length === 0) {
                console.warn('没有学生数据可用于计算平时成绩');
                targetContainer.innerHTML = '<div class="alert alert-warning">没有学生数据可用于计算平时成绩</div>';
                return;
            }

            // 计算每个学生的平时成绩
            let regularGradeByStudent = students.map(student => {
                const displayName = student.name || student.user;
                const chatCount = student.chat_views ? student.chat_views.length : 0;
                const videoCount = student.video_views ? student.video_views.length : 0;
                const homeworkAvgScore = student.homework_avg_score ? parseFloat(student.homework_avg_score) : 0;

                // 计算各项分数（使用可调整的权重）
                const homeworkScore = homeworkAvgScore * homeworkWeight; // 作业平均分 × 权重
                const chatScore = Math.min(chatCount, 25); // 提问记录分，最高25分
                const videoScore = Math.min(videoCount, 25); // 视频观看分，最高25分
                const totalRegularGrade = homeworkScore + chatScore + videoScore;

                return {
                    name: displayName,
                    homeworkAvgScore: homeworkAvgScore,
                    homeworkScore: homeworkScore,
                    homeworkWeight: homeworkWeight,
                    chatCount: chatCount,
                    chatScore: chatScore,
                    videoCount: videoCount,
                    videoScore: videoScore,
                    totalRegularGrade: totalRegularGrade
                };
            });

            // 按平时成绩降序排序
            regularGradeByStudent.sort((a, b) => b.totalRegularGrade - a.totalRegularGrade);

            // 计算班级统计信息
            const totalStudents = students.length;
            const avgRegularGrade = regularGradeByStudent.reduce((sum, item) => sum + item.totalRegularGrade, 0) / totalStudents;
            const maxRegularGrade = regularGradeByStudent.length > 0 ? regularGradeByStudent[0].totalRegularGrade : 0;
            const minRegularGrade = regularGradeByStudent.length > 0 ? regularGradeByStudent[regularGradeByStudent.length - 1].totalRegularGrade : 0;

            // 渲染平时成绩统计
            let regularGradeSummaryHtml = `
                <div class="mb-3">
                    <h5>班级平时成绩概况</h5>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h6 class="card-title">班级平均分</h6>
                                    <h4 class="text-primary">${avgRegularGrade.toFixed(1)}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h6 class="card-title">最高分</h6>
                                    <h4 class="text-success">${maxRegularGrade.toFixed(1)}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h6 class="card-title">最低分</h6>
                                    <h4 class="text-warning">${minRegularGrade.toFixed(1)}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h6 class="card-title">学生总数</h6>
                                    <h4 class="text-info">${totalStudents}</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            regularGradeSummaryHtml += `
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0">
                        <i class="bi bi-trophy-fill text-warning"></i> 平时成绩排名
                    </h5>
                    <small class="text-muted">
                        当前权重：作业×${homeworkWeight.toFixed(1)} + 提问×1 + 视频×1
                    </small>
                </div>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th><i class="bi bi-award"></i> 排名</th>
                                <th><i class="bi bi-person"></i> 学生姓名</th>
                                <th><i class="bi bi-book"></i> 作业平均分</th>
                                <th><i class="bi bi-calculator"></i> 作业得分<br><small>(×${homeworkWeight.toFixed(1)})</small></th>
                                <th><i class="bi bi-chat-dots"></i> 提问记录<br><small>(最高25)</small></th>
                                <th><i class="bi bi-play-circle"></i> 视频观看<br><small>(最高25)</small></th>
                                <th><i class="bi bi-star-fill"></i> 平时成绩</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            regularGradeByStudent.forEach((item, index) => {
                const rankBadge = index < 3 ?
                    `<span class="badge bg-${index === 0 ? 'warning' : index === 1 ? 'secondary' : 'dark'}">${index + 1}</span>` :
                    `${index + 1}`;

                regularGradeSummaryHtml += `
                    <tr>
                        <td>${rankBadge}</td>
                        <td><strong>${escapeHtml(item.name)}</strong></td>
                        <td>${item.homeworkAvgScore.toFixed(1)}</td>
                        <td>${item.homeworkScore.toFixed(1)}</td>
                        <td>${item.chatCount} → ${item.chatScore}</td>
                        <td>${item.videoCount} → ${item.videoScore}</td>
                        <td>
                            <span class="badge bg-${getRegularGradeBadgeColor(item.totalRegularGrade)} fs-6">
                                ${item.totalRegularGrade.toFixed(1)}
                            </span>
                        </td>
                    </tr>
                `;
            });

            regularGradeSummaryHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            console.log('准备更新 regularGradeSummary 容器内容');
            console.log('生成的HTML长度:', regularGradeSummaryHtml.length);

            document.getElementById('regularGradeSummary').innerHTML = regularGradeSummaryHtml;

            console.log('regularGradeSummary 容器内容已更新');

            // 存储数据供下载使用
            window.regularGradeData = regularGradeByStudent;
            console.log('已存储 regularGradeData，数据量:', regularGradeByStudent.length);
        }

        // 根据平时成绩获取对应的颜色 - 全局函数
        function getRegularGradeBadgeColor(grade) {
            if (grade >= 80) return 'success';
            if (grade >= 70) return 'primary';
            if (grade >= 60) return 'warning';
            return 'danger';
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            const params = getUrlParams();
            const user = params.user || '';
            const key = params.key || '';
            const token = params.token || '';
            
            if (!user || !key || !token) {
                document.body.innerHTML = '<div class="alert alert-danger">缺少必要的认证参数</div>';
                return;
            }
            
            // 同步按钮点击事件
            document.getElementById('syncButton').addEventListener('click', function() {
                // 显示同步中状态
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="bi bi-arrow-repeat spin"></i> 同步中...';
                this.disabled = true;
                
                // 调用同步API
                fetch(`/api/sync_class_users?user=${user}&key=${key}&token=${token}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('同步用户数据失败');
                        }
                        return response.json();
                    })
                    .then(data => {
                        // 显示成功消息
                        const syncResultDiv = document.createElement('div');
                        syncResultDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
                        syncResultDiv.innerHTML = `
                            <strong>同步成功!</strong> 已更新 ${data.updated} 个班级的学生数据。
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;
                        
                        const cardBody = document.querySelector('.card-body');
                        cardBody.insertBefore(syncResultDiv, cardBody.firstChild);
                        
                        // 如果当前选中了班级，重新加载该班级数据
                        const selectedClass = document.getElementById('classSelector').value;
                        if (selectedClass) {
                            // 触发change事件，重新加载数据
                            document.getElementById('classSelector').dispatchEvent(new Event('change'));
                        } else {
                            // 重新加载班级列表
                            loadClassList();
                        }
                        
                        // 恢复按钮状态
                        this.innerHTML = originalText;
                        this.disabled = false;
                    })
                    .catch(error => {
                        // 显示错误消息
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'alert alert-danger alert-dismissible fade show mt-3';
                        errorDiv.innerHTML = `
                            <strong>同步失败!</strong> ${error.message}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        `;
                        
                        const cardBody = document.querySelector('.card-body');
                        cardBody.insertBefore(errorDiv, cardBody.firstChild);
                        
                        // 恢复按钮状态
                        this.innerHTML = originalText;
                        this.disabled = false;
                        
                        console.error('Sync error:', error);
                    });
            });
            
            // 将班级列表加载功能提取为单独的函数，以便在同步后重新调用
            function loadClassList() {
                // 显示加载指示器
                document.getElementById('loadingIndicator').style.display = 'block';
                document.getElementById('classSelector').style.display = 'none';
                document.getElementById('noDataMessage').style.display = 'none';
                
                // 获取所有班级
                fetch(`/api/get_all_classes?user=${user}&key=${key}&token=${token}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('获取班级列表失败');
                        }
                        return response.json();
                    })
                    .then(data => {
                        const classSelector = document.getElementById('classSelector');
                        classSelector.innerHTML = '<option value="">-- 请选择班级 --</option>';
                        
                        // 清除加载提示
                        document.getElementById('loadingIndicator').style.display = 'none';
                        classSelector.style.display = 'block';
                        
                        if (data.classes && data.classes.length > 0) {
                            data.classes.forEach(classItem => {
                                const option = document.createElement('option');
                                option.value = classItem;
                                option.textContent = classItem;
                                classSelector.appendChild(option);
                            });
                        } else {
                            document.getElementById('noDataMessage').style.display = 'block';
                            document.getElementById('noDataMessage').textContent = '没有找到班级数据';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('loadingIndicator').style.display = 'none';
                        document.getElementById('noDataMessage').style.display = 'block';
                        document.getElementById('noDataMessage').textContent = '加载班级数据失败: ' + error.message;
                    });
            }
            
            // 初始加载班级列表
            loadClassList();
            
            // 班级选择事件
            document.getElementById('classSelector').addEventListener('change', function() {
                const selectedClass = this.value;
                if (!selectedClass) {
                    document.getElementById('studentDataContainer').style.display = 'none';
                    return;
                }
                
                // 显示加载提示
                document.getElementById('studentList').innerHTML = `
                    <div class="text-center p-3">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p>正在加载学生数据...</p>
                    </div>
                `;
                document.getElementById('studentDataContainer').style.display = 'block';
                
                // 获取班级学生数据
                fetch(`/api/get_class_data?class=${encodeURIComponent(selectedClass)}&user=${user}&key=${key}&token=${token}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('获取学生数据失败');
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log('获取到的班级数据:', data);
                        if (data.students) {
                            console.log(`班级 ${selectedClass} 共有 ${data.students.length} 名学生`);
                            
                            // 检查数据格式
                            if (data.students.length > 0) {
                                const firstStudent = data.students[0];
                                console.log('第一位学生示例数据:', {
                                    name: firstStudent.name || firstStudent.user,
                                    hasChat: !!firstStudent.chat_views,
                                    hasVideo: !!firstStudent.video_views,
                                    chatCount: firstStudent.chat_views ? firstStudent.chat_views.length : 0,
                                    videoCount: firstStudent.video_views ? firstStudent.video_views.length : 0
                                });
                                
                                // 确保学生有正确的数据结构
                                data.students = data.students.map(student => {
                                    return {
                                        ...student,
                                        has_records: !!(
                                            (student.chat_views && student.chat_views.length > 0) ||
                                            (student.video_views && student.video_views.length > 0)
                                        )
                                    };
                                });
                            }
                            
                            // 预加载学生作业数据
                            if (data.students && data.students.length > 0) {
                                // 获取URL参数
                                const params = getUrlParams();
                                const user = params.user || '';
                                const key = params.key || '';
                                const token = params.token || '';
                                
                                // 获取班级作业数据
                                fetch(`/api/get_homework_scores?class=${encodeURIComponent(selectedClass)}&user=${user}&key=${key}&token=${token}`)
                                    .then(response => {
                                        if (!response.ok) {
                                            console.warn('无法获取作业数据');
                                            return null;
                                        }
                                        return response.json();
                                    })
                                    .then(homeworkData => {
                                        if (homeworkData && homeworkData.students) {
                                            // 创建学生ID到作业数据的映射
                                            const studentMap = {};
                                            
                                            // 提取homework_score.json中的学生名称 (test2, test11 等)
                                            const homeworkStudentNames = Object.keys(homeworkData.students);
                                            
                                            // 首先找出班级中最大的作业提交次数
                                            let maxHomeworkCount = 0;
                                            homeworkStudentNames.forEach(studentName => {
                                                const count = Object.keys(homeworkData.students[studentName]).length;
                                                if (count > maxHomeworkCount) {
                                                    maxHomeworkCount = count;
                                                }
                                            });
                                            console.log(`班级最大作业提交次数: ${maxHomeworkCount}`);
                                            
                                            // 更新学生数据添加作业信息
                                            data.students = data.students.map(student => {
                                                // 学生名可能在作业数据中使用不同的标识
                                                const studentName = student.name || student.user;
                                                
                                                // 尝试找到匹配的学生名 - 首先尝试精确匹配
                                                let matchedName = homeworkStudentNames.find(name => 
                                                    name === studentName || 
                                                    name === student.user
                                                );
                                                
                                                // 如果没有精确匹配，尝试模糊匹配
                                                if (!matchedName) {
                                                    // 查找学生名或用户ID是否包含在作业数据的学生名中，或者作业数据的学生名包含在学生名或用户ID中
                                                    matchedName = homeworkStudentNames.find(name => 
                                                        studentName.includes(name) || 
                                                        name.includes(studentName) ||
                                                        (student.user && (student.user.includes(name) || name.includes(student.user)))
                                                    );
                                                }
                                                
                                                // 如果找到匹配的学生名，添加作业数据
                                                if (matchedName && homeworkData.students[matchedName]) {
                                                    const homeworkEntries = Object.entries(homeworkData.students[matchedName]);
                                                    const homeworkCount = homeworkEntries.length;
                                                    let totalScore = 0;
                                                    
                                                    homeworkEntries.forEach(([_, score]) => {
                                                        totalScore += score;
                                                    });
                                                    
                                                    // 使用班级最大作业提交次数作为除数，而不是学生自己的提交次数
                                                    const avgScore = homeworkCount > 0 ? (totalScore / maxHomeworkCount).toFixed(1) : 0;
                                                    
                                                    // 存储映射关系，以便后续使用
                                                    studentMap[studentName] = matchedName;
                                                    
                                                    return {
                                                        ...student,
                                                        homework: homeworkData.students[matchedName],
                                                        homework_count: homeworkCount,
                                                        homework_avg_score: avgScore,
                                                        homework_data_name: matchedName // 保存在作业数据中的学生名称，用于后续查询
                                                    };
                                                }
                                                return student;
                                            });
                                            
                                            // 将学生ID到作业数据名称的映射存储在window对象上，以便后续使用
                                            window.studentHomeworkMap = studentMap;
                                        }
                                        // 渲染学生列表
                                        renderStudentList(selectedClass, data.students);
                                        renderClassSummary(data.students);
                                        // 保存学生数据供平时成绩重新计算使用
                                        window.currentStudentData = data.students;
                                        renderRegularGradeSummary(data.students);

                                        // 自动加载问题统计（使用缓存）
                                        loadQuestionStatistics(selectedClass, false);
                                    })
                                    .catch(error => {
                                        console.error('获取作业数据失败:', error);
                                        // 即使获取作业失败，仍然继续渲染学生列表
                                        renderStudentList(selectedClass, data.students);
                                        renderClassSummary(data.students);
                                        // 保存学生数据供平时成绩重新计算使用
                                        window.currentStudentData = data.students;
                                        renderRegularGradeSummary(data.students);

                                        // 自动加载问题统计（使用缓存）
                                        loadQuestionStatistics(selectedClass, false);
                                    });
                            } else {
                                renderStudentList(selectedClass, data.students);
                                renderClassSummary(data.students); // 添加渲染班级汇总信息
                                // 保存学生数据供平时成绩重新计算使用
                                window.currentStudentData = data.students;
                                renderRegularGradeSummary(data.students);

                                // 自动加载问题统计（使用缓存）
                                loadQuestionStatistics(selectedClass, false);
                            }
                            
                            // 初始化所有Bootstrap选项卡
                            setTimeout(() => {
                                document.querySelectorAll('[data-bs-toggle="tab"]').forEach(tabEl => {
                                    try {
                                        new bootstrap.Tab(tabEl);
                                    } catch(e) {
                                        console.warn('初始化选项卡时出错:', e);
                                    }
                                });
                                console.log('已初始化所有选项卡');
                            }, 500);
                        } else {
                            document.getElementById('studentList').innerHTML = '<div class="alert alert-warning">没有找到学生数据</div>';
                            document.getElementById('chatSummary').innerHTML = '<div class="alert alert-warning">没有找到学生数据</div>';
                            document.getElementById('videoSummary').innerHTML = '<div class="alert alert-warning">没有找到学生数据</div>';
                            document.getElementById('activityRanking').innerHTML = '<div class="alert alert-warning">没有找到学生数据</div>';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('studentList').innerHTML = `<div class="alert alert-danger">加载学生数据失败: ${error.message}</div>`;
                        document.getElementById('chatSummary').innerHTML = '<div class="alert alert-danger">加载学生数据失败</div>';
                        document.getElementById('videoSummary').innerHTML = '<div class="alert alert-danger">加载学生数据失败</div>';
                        document.getElementById('activityRanking').innerHTML = '<div class="alert alert-danger">加载学生数据失败</div>';
                    });
            });
            
            // 渲染学生列表
            function renderStudentList(className, students) {
                const studentList = document.getElementById('studentList');
                
                // 清除之前的事件监听器(先克隆并替换元素)
                const newStudentList = studentList.cloneNode(false);
                studentList.parentNode.replaceChild(newStudentList, studentList);
                
                console.log(`开始渲染学生列表，共 ${students.length} 名学生`);
                
                // 更新班级标题和学生数量
                document.getElementById('classTitle').textContent = `${className} 班级学生列表`;
                document.getElementById('studentCount').textContent = `共 ${students.length} 名学生`;
                
                // 按照姓名排序
                students.sort((a, b) => {
                    const nameA = a.name || a.user;
                    const nameB = b.name || b.user;
                    return nameA.localeCompare(nameB, 'zh-CN');
                });
                
                // 记录一下每个学生的基本信息
                students.forEach((student, index) => {
                    console.log(`学生 ${index+1}: ${student.name || student.user}, 提问记录: ${student.chat_views ? student.chat_views.length : 0}, 视频记录: ${student.video_views ? student.video_views.length : 0}`);
                });
                
                // 创建一个文档片段来提高性能
                const fragment = document.createDocumentFragment();
                
                students.forEach(student => {
                    const listItem = document.createElement('li');
                    listItem.className = 'student-list-item';
                    listItem.setAttribute('data-user', student.user);
                    
                    // 添加直接的点击事件处理
                    listItem.addEventListener('click', function(e) {
                        // 获取点击目标的最近的.nav-link祖先元素
                        const targetNavLink = e.target.closest('.nav-link');
                        // 获取点击目标的最近的.student-details祖先元素
                        const targetDetails = e.target.closest('.student-details');
                        
                        // 如果点击的是标签页按钮，阻止事件冒泡，避免学生列表项的点击事件处理
                        if (targetNavLink) {
                            e.stopPropagation();
                            return; // 由标签页自己的点击事件处理
                        }
                        
                        // 如果点击的是已经展开的详情区域内部（但不是标签页按钮），阻止折叠
                        if (targetDetails && targetDetails.style.display === 'block') {
                            console.log('点击在已展开的详情区域内部，不触发折叠');
                            e.stopPropagation();
                            return;
                        }
                        
                        // 其他情况（点击在学生列表项上，非详情区域内），切换详情显示
                        console.log(`学生 ${student.user} 的列表项被点击，准备切换详情展示`);
                        toggleStudentDetails(this);
                    });
                    
                    // 计算提问记录和视频观看数量
                    const chatCount = student.chat_views ? student.chat_views.length : 0;
                    const videoCount = student.video_views ? student.video_views.length : 0;
                    const homeworkCount = student.homework_count || 0;
                    const hasRecords = chatCount > 0 || videoCount > 0;
                    
                    // 使用学生姓名（如果有）或用户名
                    const displayName = student.name || student.user;
                    
                    // 创建学生信息区域
                    const studentInfoDiv = document.createElement('div');
                    studentInfoDiv.className = 'student-info';
                    
                    // 添加学生姓名
                    const nameSpan = document.createElement('span');
                    nameSpan.className = 'student-name';
                    nameSpan.textContent = displayName;
                    studentInfoDiv.appendChild(nameSpan);
                    
                    // 添加记录状态指示
                    if (hasRecords || homeworkCount > 0) {
                        const badgesDiv = document.createElement('div');
                        badgesDiv.className = 'badges-container';
                        
                        if (chatCount > 0) {
                            const chatBadge = document.createElement('span');
                            chatBadge.className = 'badge bg-primary student-badge';
                            chatBadge.textContent = `提问: ${chatCount}`;
                            badgesDiv.appendChild(chatBadge);
                        }
                        
                        if (videoCount > 0) {
                            const videoBadge = document.createElement('span');
                            videoBadge.className = 'badge bg-success student-badge';
                            videoBadge.textContent = `视频: ${videoCount}`;
                            badgesDiv.appendChild(videoBadge);
                        }
                        
                        if (homeworkCount > 0) {
                            const homeworkBadge = document.createElement('span');
                            homeworkBadge.className = 'badge bg-warning student-badge';
                            homeworkBadge.textContent = `作业: ${homeworkCount}`;
                            if (student.homework_avg_score) {
                                homeworkBadge.textContent += ` (${student.homework_avg_score}分)`;
                            }
                            badgesDiv.appendChild(homeworkBadge);
                        }
                        
                        studentInfoDiv.appendChild(badgesDiv);
                    } else {
                        const noRecordsSpan = document.createElement('span');
                        noRecordsSpan.className = 'no-records';
                        noRecordsSpan.textContent = '无浏览记录';
                        studentInfoDiv.appendChild(noRecordsSpan);
                    }
                    
                    listItem.appendChild(studentInfoDiv);
                    
                    // 添加展开/折叠指示器
                    const indicatorSpan = document.createElement('span');
                    indicatorSpan.className = 'student-indicator';
                    indicatorSpan.innerHTML = '<i class="bi bi-chevron-down"></i>';
                    listItem.appendChild(indicatorSpan);
                    
                    // 创建详细信息区域 - 为所有学生创建，但内容不同
                    const detailsDiv = document.createElement('div');
                    detailsDiv.className = 'student-details';
                    detailsDiv.id = `details-${escapeHtml(student.user)}`;
                    detailsDiv.style.display = 'none'; // 确保初始状态为隐藏
                    
                    // 创建唯一的ID前缀，确保ID的唯一性
                    // 使用base64编码确保唯一性并避免特殊字符问题
                    const uniqueUserId = btoa(encodeURIComponent(student.user)).replace(/[=+/]/g, '');
                    const idPrefix = `student-${uniqueUserId}-`;
                    const chatTabId = `${idPrefix}chat-tab`;
                    const videoTabId = `${idPrefix}video-tab`;
                    const homeworkTabId = `${idPrefix}homework-tab`;
                    const chatContentId = `${idPrefix}chat-content`;
                    const videoContentId = `${idPrefix}video-content`;
                    const homeworkContentId = `${idPrefix}homework-content`;
                    
                    // 创建标签页容器
                    const navTabsUl = document.createElement('ul');
                    navTabsUl.className = 'nav nav-tabs';
                    navTabsUl.setAttribute('role', 'tablist');
                    
                    // 创建提问记录标签页
                    const chatTabLi = document.createElement('li');
                    chatTabLi.className = 'nav-item';
                    chatTabLi.setAttribute('role', 'presentation');
                    
                    const chatTabButton = document.createElement('button');
                    chatTabButton.className = 'nav-link active';
                    chatTabButton.id = chatTabId;
                    chatTabButton.setAttribute('data-bs-toggle', 'tab');
                    chatTabButton.setAttribute('data-bs-target', `#${chatContentId}`);
                    chatTabButton.setAttribute('type', 'button');
                    chatTabButton.setAttribute('role', 'tab');
                    chatTabButton.setAttribute('aria-selected', 'true');
                    chatTabButton.textContent = `提问记录 (${chatCount})`;
                    
                    // 添加点击事件
                    chatTabButton.addEventListener('click', function(e) {
                        e.stopPropagation(); // 阻止事件冒泡
                        e.preventDefault();
                        console.log(`提问标签页 ${chatTabId} 被点击`);
                        try {
                            // 存储chatContentId作为自定义属性，避免再次查找
                            this.setAttribute('data-content-id', chatContentId);
                            showTabContent(detailsDiv, this, chatContentId);
                        } catch (err) {
                            console.error('切换到提问标签页失败：', err);
                        }
                    });
                    
                    chatTabLi.appendChild(chatTabButton);
                    navTabsUl.appendChild(chatTabLi);
                    
                    // 创建视频观看标签页
                    const videoTabLi = document.createElement('li');
                    videoTabLi.className = 'nav-item';
                    videoTabLi.setAttribute('role', 'presentation');
                    
                    const videoTabButton = document.createElement('button');
                    videoTabButton.className = 'nav-link';
                    videoTabButton.id = videoTabId;
                    videoTabButton.setAttribute('data-bs-toggle', 'tab');
                    videoTabButton.setAttribute('data-bs-target', `#${videoContentId}`);
                    videoTabButton.setAttribute('type', 'button');
                    videoTabButton.setAttribute('role', 'tab');
                    videoTabButton.setAttribute('aria-selected', 'false');
                    videoTabButton.textContent = `视频观看 (${videoCount})`;
                    
                    // 添加点击事件
                    videoTabButton.addEventListener('click', function(e) {
                        e.stopPropagation(); // 阻止事件冒泡
                        e.preventDefault();
                        console.log(`视频标签页 ${videoTabId} 被点击`);
                        try {
                            // 存储videoContentId作为自定义属性，避免再次查找
                            this.setAttribute('data-content-id', videoContentId);
                            showTabContent(detailsDiv, this, videoContentId);
                        } catch (err) {
                            console.error('切换到视频标签页失败：', err);
                        }
                    });
                    
                    videoTabLi.appendChild(videoTabButton);
                    navTabsUl.appendChild(videoTabLi);
                    
                    // 创建作业标签页
                    const homeworkTabLi = document.createElement('li');
                    homeworkTabLi.className = 'nav-item';
                    homeworkTabLi.setAttribute('role', 'presentation');
                    
                    const homeworkTabButton = document.createElement('button');
                    homeworkTabButton.className = 'nav-link';
                    homeworkTabButton.id = homeworkTabId;
                    homeworkTabButton.setAttribute('data-bs-toggle', 'tab');
                    homeworkTabButton.setAttribute('data-bs-target', `#${homeworkContentId}`);
                    homeworkTabButton.setAttribute('type', 'button');
                    homeworkTabButton.setAttribute('role', 'tab');
                    homeworkTabButton.setAttribute('aria-selected', 'false');
                    homeworkTabButton.textContent = `作业成绩 (${homeworkCount})`;
                    
                    // 添加点击事件
                    homeworkTabButton.addEventListener('click', function(e) {
                        e.stopPropagation(); // 阻止事件冒泡
                        e.preventDefault();
                        console.log(`作业标签页 ${homeworkTabId} 被点击`);
                        try {
                            // 存储homeworkContentId作为自定义属性，避免再次查找
                            this.setAttribute('data-content-id', homeworkContentId);
                            showTabContent(detailsDiv, this, homeworkContentId);
                            
                            // 加载该学生的作业数据，如果还没有加载
                            if (!this.getAttribute('data-loaded')) {
                                loadStudentHomework(student.user, className, homeworkContentId);
                                this.setAttribute('data-loaded', 'true');
                            }
                        } catch (err) {
                            console.error('切换到作业标签页失败：', err);
                        }
                    });
                    
                    homeworkTabLi.appendChild(homeworkTabButton);
                    navTabsUl.appendChild(homeworkTabLi);
                    
                    // 为所有学生添加标签页，无论是否有记录
                    detailsDiv.appendChild(navTabsUl);
                    
                    // 创建内容区域容器
                    const tabContentDiv = document.createElement('div');
                    tabContentDiv.className = 'tab-content mt-3';
                    
                    // 提问记录内容
                    const chatContentDiv = document.createElement('div');
                    chatContentDiv.className = 'tab-pane fade show active';
                    chatContentDiv.id = chatContentId;
                    chatContentDiv.setAttribute('role', 'tabpanel');
                    chatContentDiv.setAttribute('aria-labelledby', chatTabId);
                    
                    if (student.chat_views && student.chat_views.length > 0) {
                        // 按时间倒序排列提问记录
                        const sortedChatViews = [...student.chat_views].sort((a, b) => 
                            new Date(b.timestamp) - new Date(a.timestamp)
                        );
                        
                        sortedChatViews.forEach(chat => {
                            const chatItem = document.createElement('div');
                            chatItem.className = 'chat-item';
                            
                            const messageDiv = document.createElement('div');
                            messageDiv.innerHTML = `<strong>消息:</strong> ${escapeHtml(chat.message || '无消息内容')}`;
                            
                            const fileDiv = document.createElement('div');
                            fileDiv.innerHTML = `<strong>文件:</strong> ${escapeHtml(chat.filename || '无文件')}`;
                            
                            const timeDiv = document.createElement('div');
                            timeDiv.className = 'timestamp';
                            timeDiv.textContent = chat.timestamp;
                            
                            chatItem.appendChild(messageDiv);
                            chatItem.appendChild(fileDiv);
                            chatItem.appendChild(timeDiv);
                            
                            chatContentDiv.appendChild(chatItem);
                        });
                    } else {
                        const noRecordsP = document.createElement('p');
                        noRecordsP.className = 'text-muted';
                        noRecordsP.textContent = '没有提问记录';
                        chatContentDiv.appendChild(noRecordsP);
                    }
                    
                    // 视频观看内容
                    const videoContentDiv = document.createElement('div');
                    videoContentDiv.className = 'tab-pane fade';
                    videoContentDiv.id = videoContentId;
                    videoContentDiv.setAttribute('role', 'tabpanel');
                    videoContentDiv.setAttribute('aria-labelledby', videoTabId);
                    
                    if (student.video_views && student.video_views.length > 0) {
                        // 按时间倒序排列视频记录
                        const sortedVideoViews = [...student.video_views].sort((a, b) => 
                            new Date(b.timestamp) - new Date(a.timestamp)
                        );
                        
                        sortedVideoViews.forEach(video => {
                            const videoItem = document.createElement('div');
                            videoItem.className = 'video-item';
                            
                            const videoDiv = document.createElement('div');
                            videoDiv.innerHTML = `<strong>视频:</strong> ${escapeHtml(video.video_filename || '未知视频')}`;
                            
                            const timeDiv = document.createElement('div');
                            timeDiv.className = 'timestamp';
                            timeDiv.textContent = video.timestamp;
                            
                            videoItem.appendChild(videoDiv);
                            videoItem.appendChild(timeDiv);
                            
                            videoContentDiv.appendChild(videoItem);
                        });
                    } else {
                        const noRecordsP = document.createElement('p');
                        noRecordsP.className = 'text-muted';
                        noRecordsP.textContent = '没有视频观看记录';
                        videoContentDiv.appendChild(noRecordsP);
                    }
                    
                    // 作业内容
                    const homeworkContentDiv = document.createElement('div');
                    homeworkContentDiv.className = 'tab-pane fade';
                    homeworkContentDiv.id = homeworkContentId;
                    homeworkContentDiv.setAttribute('role', 'tabpanel');
                    homeworkContentDiv.setAttribute('aria-labelledby', homeworkTabId);
                    homeworkContentDiv.innerHTML = `
                        <div class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">加载中...</span>
                            </div>
                            <p>加载作业数据...</p>
                        </div>
                    `;
                    
                    // 添加内容到tab内容区域
                    tabContentDiv.appendChild(chatContentDiv);
                    tabContentDiv.appendChild(videoContentDiv);
                    tabContentDiv.appendChild(homeworkContentDiv);
                    
                    // 添加内容区域到详情区域
                    detailsDiv.appendChild(tabContentDiv);
                    
                    listItem.appendChild(detailsDiv);
                    fragment.appendChild(listItem);
                });
                
                // 一次性添加所有列表项
                newStudentList.appendChild(fragment);
                
                // 移除旧的标签页初始化代码
                setTimeout(() => {
                    console.log('学生列表渲染完成，共渲染了 ' + students.length + ' 名学生');
                }, 100);
                
                console.log(`学生列表渲染完成，共渲染了 ${students.length} 名学生`);
            }
            
            // 全局函数：切换学生详情显示状态
            function toggleStudentDetails(listItem) {
                if (!listItem) return;
                
                const detailsElement = listItem.querySelector('.student-details');
                const indicatorElement = listItem.querySelector('.student-indicator');
                const username = listItem.getAttribute('data-user');
                const displayName = listItem.querySelector('.student-name').textContent;
                
                if (!detailsElement) {
                    console.error(`未找到学生 ${displayName} 的详情元素`);
                    return;
                }
                
                // 判断当前是否已展开
                const isDisplayed = window.getComputedStyle(detailsElement).display !== 'none';
                console.log(`学生 ${displayName} 的详情当前显示状态:`, isDisplayed ? '显示中' : '隐藏中');
                
                // 先隐藏所有展开的详情
                document.querySelectorAll('.student-details').forEach(el => {
                    el.style.display = 'none';
                });
                document.querySelectorAll('.student-indicator').forEach(el => {
                    el.innerHTML = '<i class="bi bi-chevron-down"></i>';
                });
                document.querySelectorAll('.student-list-item').forEach(el => {
                    el.classList.remove('active');
                });
                
                // 如果之前是隐藏状态，则显示；否则保持隐藏状态
                if (!isDisplayed) {
                    // 显示当前学生的详情
                    detailsElement.style.display = 'block';
                    indicatorElement.innerHTML = '<i class="bi bi-chevron-up"></i>';
                    listItem.classList.add('active');
                    console.log(`展开学生 ${displayName}(${username}) 的详细信息`);
                    
                    // 确保当前内容在视图中，并选择第一个标签页
                    setTimeout(() => {
                        detailsElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                        
                        // 自动激活第一个标签页
                        const firstTabButton = detailsElement.querySelector('.nav-link');
                        if (firstTabButton) {
                            // 找到关联的内容ID
                            const contentId = firstTabButton.getAttribute('data-content-id') || 
                                             firstTabButton.getAttribute('data-bs-target')?.replace('#', '');
                            if (contentId) {
                                console.log(`自动激活第一个标签页: ${contentId}`);
                                showTabContent(detailsElement, firstTabButton, contentId);
                            }
                        }
                    }, 100);
                } else {
                    console.log(`关闭学生 ${displayName} 的详细信息`);
                }
            }
            
            // 渲染班级汇总信息
            function renderClassSummary(students) {
                // 计算提问记录统计
                let totalChatViews = 0;
                let chatStudentsCount = 0;
                let chatViewsByStudent = [];
                
                // 计算视频观看统计
                let totalVideoViews = 0;
                let videoStudentsCount = 0;
                let videoViewsByStudent = [];
                
                // 计算作业统计
                let totalHomeworkCount = 0;
                let homeworkStudentsCount = 0;
                let totalHomeworkScore = 0;
                let homeworkByStudent = [];
                
                // 处理每个学生的数据
                students.forEach(student => {
                    const chatCount = student.chat_views ? student.chat_views.length : 0;
                    const videoCount = student.video_views ? student.video_views.length : 0;
                    const homeworkCount = student.homework_count || 0;
                    const homeworkAvgScore = student.homework_avg_score ? parseFloat(student.homework_avg_score) : 0;
                    const displayName = student.name || student.user;
                    
                    // 提问记录统计
                    if (chatCount > 0) {
                        chatStudentsCount++;
                        totalChatViews += chatCount;
                        chatViewsByStudent.push({
                            name: displayName,
                            count: chatCount
                        });
                    }
                    
                    // 视频观看统计
                    if (videoCount > 0) {
                        videoStudentsCount++;
                        totalVideoViews += videoCount;
                        videoViewsByStudent.push({
                            name: displayName,
                            count: videoCount
                        });
                    }
                    
                    // 作业统计
                    if (homeworkCount > 0) {
                        homeworkStudentsCount++;
                        totalHomeworkCount += homeworkCount;
                        totalHomeworkScore += homeworkAvgScore * homeworkCount; // 加权平均
                        
                        homeworkByStudent.push({
                            name: displayName,
                            count: homeworkCount,
                            avgScore: homeworkAvgScore
                        });
                    }
                });
                
                // 按照记录数量排序
                chatViewsByStudent.sort((a, b) => b.count - a.count);
                videoViewsByStudent.sort((a, b) => b.count - a.count);
                // 按照平均分排序
                homeworkByStudent.sort((a, b) => b.avgScore - a.avgScore);
                
                // 渲染提问记录统计
                let chatSummaryHtml = `
                    <div class="mb-3">
                        <h5>总览</h5>
                        <p>总提问记录数: <strong>${totalChatViews}</strong></p>
                        <p>有提问记录的学生数: <strong>${chatStudentsCount}</strong> / ${students.length}</p>
                        <p>平均每名学生提问记录: <strong>${(totalChatViews / students.length).toFixed(2)}</strong></p>
                    </div>
                `;
                
                if (chatViewsByStudent.length > 0) {
                    chatSummaryHtml += `
                        <h5>提问记录排名</h5>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>学生</th>
                                        <th>记录数</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;
                    
                    chatViewsByStudent.slice(0, 10).forEach((item, index) => {
                        chatSummaryHtml += `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${escapeHtml(item.name)}</td>
                                <td>${item.count}</td>
                            </tr>
                        `;
                    });
                    
                    chatSummaryHtml += `
                                </tbody>
                            </table>
                        </div>
                    `;
                }
                
                document.getElementById('chatSummary').innerHTML = chatSummaryHtml;
                
                // 渲染视频观看统计
                let videoSummaryHtml = `
                    <div class="mb-3">
                        <h5>总览</h5>
                        <p>总视频观看记录数: <strong>${totalVideoViews}</strong></p>
                        <p>有视频观看记录的学生数: <strong>${videoStudentsCount}</strong> / ${students.length}</p>
                        <p>平均每名学生视频观看记录: <strong>${(totalVideoViews / students.length).toFixed(2)}</strong></p>
                    </div>
                `;
                
                if (videoViewsByStudent.length > 0) {
                    videoSummaryHtml += `
                        <h5>视频观看排名</h5>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>学生</th>
                                        <th>记录数</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;
                    
                    videoViewsByStudent.slice(0, 10).forEach((item, index) => {
                        videoSummaryHtml += `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${escapeHtml(item.name)}</td>
                                <td>${item.count}</td>
                            </tr>
                        `;
                    });
                    
                    videoSummaryHtml += `
                                </tbody>
                            </table>
                        </div>
                    `;
                }
                
                document.getElementById('videoSummary').innerHTML = videoSummaryHtml;
                
                // 渲染作业统计 - 修改为直接查找homeworkSummary元素
                let homeworkSummaryDiv = document.getElementById('homeworkSummary');
                if (homeworkSummaryDiv) {
                    // 计算班级平均分
                    // 首先找出班级中最大的作业提交次数
                    let maxHomeworkCount = 0;
                    students.forEach(student => {
                        const count = student.homework_count || 0;
                        if (count > maxHomeworkCount) {
                            maxHomeworkCount = count;
                        }
                    });
                    
                    // 计算班级平均分 - 使用总分除以(学生数×最大作业数)而不是除以实际提交数
                    const possibleTotalSubmissions = students.length * maxHomeworkCount;
                    const classAvgScore = possibleTotalSubmissions > 0 ? (totalHomeworkScore / possibleTotalSubmissions).toFixed(2) : 0;
                    
                    // 创建所有学生的作业统计数据，包括没有作业的学生
                    const allStudentsHomework = students.map(student => {
                        const displayName = student.name || student.user;
                        const homeworkCount = student.homework_count || 0;
                        const homeworkAvgScore = student.homework_avg_score ? parseFloat(student.homework_avg_score) : 0;
                        
                        return {
                            name: displayName,
                            count: homeworkCount,
                            avgScore: homeworkAvgScore
                        };
                    });
                    
                    // 按照平均分排序
                    allStudentsHomework.sort((a, b) => b.avgScore - a.avgScore);
                    
                    let homeworkSummaryHtml = `
                        <div class="mb-3">
                            <h5>总览</h5>
                            <p>总作业提交数: <strong>${totalHomeworkCount}</strong></p>
                            <p>有作业的学生数: <strong>${homeworkStudentsCount}</strong> / ${students.length}</p>
                            <p>平均每名学生作业数: <strong>${(totalHomeworkCount / students.length).toFixed(2)}</strong></p>
                            <p>班级平均分: <strong>${classAvgScore}</strong></p>
                        </div>
                    `;
                    
                    homeworkSummaryHtml += `
                        <h5>作业成绩排名</h5>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>排名</th>
                                        <th>学生</th>
                                        <th>作业数</th>
                                        <th>平均分</th>
                                    </tr>
                                </thead>
                                <tbody>
                    `;
                    
                    allStudentsHomework.forEach((item, index) => {
                        homeworkSummaryHtml += `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${escapeHtml(item.name)}</td>
                                <td>${item.count}</td>
                                <td>
                                    ${item.avgScore > 0 ? 
                                        `<span class="badge bg-${getScoreBadgeColor(item.avgScore)}">${item.avgScore}</span>` : 
                                        '0'}
                                </td>
                            </tr>
                        `;
                    });
                    
                    homeworkSummaryHtml += `
                                </tbody>
                            </table>
                        </div>
                    `;
                    
                    homeworkSummaryDiv.innerHTML = homeworkSummaryHtml;
                }
                
                // 渲染活跃度排名
                // 计算总活跃度 (提问记录 + 视频观看)
                let activityByStudent = students.map(student => {
                    const chatCount = student.chat_views ? student.chat_views.length : 0;
                    const videoCount = student.video_views ? student.video_views.length : 0;
                    const totalActivity = chatCount + videoCount;
                    return {
                        name: student.name || student.user,
                        chatCount,
                        videoCount,
                        totalActivity
                    };
                });
                
                // 按总活跃度排序
                activityByStudent.sort((a, b) => b.totalActivity - a.totalActivity);
                
                let activityRankingHtml = `
                    <div class="table-responsive">
                        <table class="table table-sm table-striped">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>学生</th>
                                    <th>提问记录</th>
                                    <th>视频观看</th>
                                    <th>总活跃度</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                // 显示所有学生，而不是仅前20名
                activityByStudent.forEach((item, index) => {
                    activityRankingHtml += `
                        <tr>
                            <td>${index + 1}</td>
                            <td>${escapeHtml(item.name)}</td>
                            <td>${item.chatCount}</td>
                            <td>${item.videoCount}</td>
                            <td>${item.totalActivity}</td>
                        </tr>
                    `;
                });
                
                activityRankingHtml += `
                            </tbody>
                        </table>
                    </div>
                `;
                
                document.getElementById('activityRanking').innerHTML = activityRankingHtml;
            }




            // 显示标签页内容的函数
            function showTabContent(container, tabButton, contentId) {
                if (!container) {
                    console.error('showTabContent: 容器为空');
                    return;
                }
                if (!tabButton) {
                    console.error('showTabContent: 标签按钮为空');
                    return;
                }
                if (!contentId) {
                    console.error('showTabContent: 内容ID为空');
                    return;
                }
                
                console.log(`处理标签页切换: 切换到 ${contentId}`);
                console.log('标签页容器:', container);
                console.log('标签页按钮:', tabButton);
                
                try {
                    // 先将所有标签设为非活动 - 只影响当前容器内的标签
                    const allNavLinks = container.querySelectorAll('.nav-link');
                    console.log(`找到 ${allNavLinks.length} 个标签`);
                    allNavLinks.forEach(link => {
                        link.classList.remove('active');
                        link.setAttribute('aria-selected', 'false');
                    });
                    
                    // 将当前标签设为活动
                    tabButton.classList.add('active');
                    tabButton.setAttribute('aria-selected', 'true');
                    
                    // 先将所有内容面板设为非活动 - 只影响当前容器内的面板
                    const allPanes = container.querySelectorAll('.tab-pane');
                    console.log(`找到 ${allPanes.length} 个内容面板`);
                    allPanes.forEach(pane => {
                        pane.classList.remove('show', 'active');
                    });
                    
                    // 只在当前容器内查找内容面板，而不是整个文档
                    const contentPane = container.querySelector(`#${contentId}`);
                    if (contentPane) {
                        contentPane.classList.add('show', 'active');
                        console.log(`成功激活内容面板 ${contentId}`);
                        
                        // 确保内容面板在视图中
                        contentPane.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    } else {
                        console.error(`在容器内找不到内容面板 ID=${contentId}`);
                        // 尝试用其他方式查找
                        const contentPaneBySelector = container.querySelector(`[id="${contentId}"]`) || 
                                                      document.getElementById(contentId);
                        if (contentPaneBySelector) {
                            contentPaneBySelector.classList.add('show', 'active');
                            console.log(`通过替代方法找到并激活内容面板 ${contentId}`);
                            // 确保内容面板在视图中
                            contentPaneBySelector.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                        } else {
                            console.error(`所有方法都无法找到内容面板 ${contentId}，检查是否有ID冲突`);
                            // 输出一些调试信息
                            console.log('内容面板候选者:', container.querySelectorAll('.tab-pane'));
                        }
                    }
                } catch (err) {
                    console.error('标签页切换出错:', err, err.stack);
                }
            }
            
            // 添加问题统计功能
            function setupQuestionStatistics() {
                const generateQuestionsBtn = document.getElementById('generateQuestionsBtn');
                if (!generateQuestionsBtn) return;
                
                generateQuestionsBtn.addEventListener('click', function() {
                    const selectedClass = document.getElementById('classSelector').value;
                    if (!selectedClass) {
                        showQuestionsError('请先选择一个班级');
                        return;
                    }
                    
                    // 强制刷新分析
                    loadQuestionStatistics(selectedClass, true);
                });
            }
            
            // 加载问题统计数据
            function loadQuestionStatistics(className, forceRefresh) {
                if (!className) return;
                
                // 显示加载中
                document.getElementById('questionsLoadingIndicator').style.display = 'block';
                document.getElementById('questionsErrorMessage').style.display = 'none';
                document.getElementById('questionsContent').innerHTML = '';
                
                // 调用服务器上已有的分析接口
                const params = getUrlParams();
                const apiUrl = `/api/analyze?class=${encodeURIComponent(className)}&user=${params.user || ''}&key=${params.key || ''}&token=${params.token || ''}${forceRefresh ? '&force_refresh=true' : ''}`;
                
                console.log(`调用问题分析接口${forceRefresh ? '(强制刷新)' : '(优先使用缓存)'}:`, apiUrl);
                
                fetch(apiUrl)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(response.status === 403 ? '没有权限进行此操作' : '分析问题失败');
                        }
                        return response.json();
                    })
                    .then(data => {
                        document.getElementById('questionsLoadingIndicator').style.display = 'none';
                        
                        if (data.success && data.questions && data.questions.length > 0) {
                            console.log('成功获取问题分析结果:', data);
                            renderQuestionsList(data.questions);
                            
                            // 显示消息统计和缓存信息
                            const statusClass = data.from_cache ? 'alert-info' : 'alert-success';
                            const cacheInfo = data.from_cache ? 
                                `从缓存加载的结果（${data.cache_time || '未知时间'}）` : 
                                `实时分析的结果（${data.cache_time || '未知时间'}）`;
                            
                            const successMsg = document.createElement('div');
                            successMsg.className = `alert ${statusClass} mt-3`;
                            successMsg.innerHTML = `
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>分析完成！共处理了 <strong>${data.message_count || 0}</strong> 条消息，找到 <strong>${data.questions.length}</strong> 个共性问题。</div>
                                    <div><small class="text-muted">${cacheInfo}</small></div>
                                </div>
                            `;
                            document.getElementById('questionsContent').insertBefore(successMsg, document.getElementById('questionsContent').firstChild);
                            
                            // 如果是自动加载的缓存结果，显示提示
                            if (data.from_cache && !forceRefresh) {
                                const refreshTip = document.createElement('div');
                                refreshTip.className = 'alert alert-light text-center mt-3';
                                refreshTip.innerHTML = `
                                    <small>这是缓存的分析结果。如需重新分析，请点击"生成问题统计"按钮。</small>
                                `;
                                document.getElementById('questionsContent').appendChild(refreshTip);
                            }
                        } else {
                            showQuestionsError(data.message || '没有找到足够的问题数据进行分析');
                        }
                    })
                    .catch(error => {
                        console.error('问题分析错误:', error);
                        document.getElementById('questionsLoadingIndicator').style.display = 'none';
                        showQuestionsError(error.message);
                    });
            }
            
            // 显示问题统计错误
            function showQuestionsError(message) {
                const errorElement = document.getElementById('questionsErrorMessage');
                errorElement.textContent = message;
                errorElement.style.display = 'block';
                document.getElementById('questionsContent').innerHTML = '';
            }
            
            // 渲染问题列表
            function renderQuestionsList(questions) {
                const questionsContent = document.getElementById('questionsContent');
                
                let html = `
                    <div class="mb-4">
                        <h5>学生常见问题 Top ${questions.length}</h5>
                        <p class="text-muted">以下是根据学生提问记录分析出的最常见问题</p>
                    </div>
                    <div class="list-group">
                `;
                
                questions.forEach((question, index) => {
                    html += `
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">${index + 1}. ${escapeHtml(question.question)}</h5>
                                ${question.frequency ? `<span class="badge bg-primary rounded-pill">${question.frequency} 次</span>` : ''}
                            </div>
                            ${question.explanation ? `<p class="mb-1">${escapeHtml(question.explanation)}</p>` : ''}
                            ${question.examples && question.examples.length > 0 ? 
                                `<small class="text-muted">
                                    <strong>示例:</strong> ${escapeHtml(question.examples[0])}
                                    ${question.examples.length > 1 ? `<span class="text-muted">(等 ${question.examples.length} 条)</span>` : ''}
                                </small>` : ''
                            }
                        </div>
                    `;
                });
                
                html += '</div>';
                questionsContent.innerHTML = html;
            }
            
            // 修改预加载学生作业数据部分，添加学生名称与homework_score.json中名称的映射逻辑
            function loadStudentHomework(student, className, contentId) {
                if (!student || !className || !contentId) {
                    console.error('加载作业失败：缺少必要参数');
                    return;
                }
                
                const contentDiv = document.getElementById(contentId);
                if (!contentDiv) {
                    console.error(`找不到作业内容区域 ID=${contentId}`);
                    return;
                }
                
                // 显示加载中
                contentDiv.innerHTML = `
                    <div class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                        <p>加载作业数据...</p>
                    </div>
                `;
                
                // 获取URL参数以便API请求
                const params = getUrlParams();
                const user = params.user || '';
                const key = params.key || '';
                const token = params.token || '';
                
                // 先尝试直接获取作业数据
                const studentElement = document.querySelector(`[data-user="${student}"]`);
                if (studentElement) {
                    const studentObj = Array.from(document.querySelectorAll('.student-list-item'))
                        .find(item => item.getAttribute('data-user') === student);
                        
                    if (studentObj && studentObj.__studentData && studentObj.__studentData.homework) {
                        renderHomeworkData(contentDiv, studentObj.__studentData.homework);
                        return;
                    }
                }
                
                // 查找映射关系中的学生名
                const mappedStudentName = window.studentHomeworkMap && window.studentHomeworkMap[student] ? 
                    window.studentHomeworkMap[student] : student;
                
                // 调用API获取作业数据，使用映射后的学生名
                fetch(`/api/get_homework_scores?class=${encodeURIComponent(className)}&student=${encodeURIComponent(mappedStudentName)}&user=${user}&key=${key}&token=${token}`)
                    .then(response => {
                        if (!response.ok) {
                            // 尝试获取班级所有数据，然后过滤目标学生
                            return fetch(`/api/get_homework_scores?class=${encodeURIComponent(className)}&user=${user}&key=${key}&token=${token}`)
                                .then(classResponse => {
                                    if (!classResponse.ok) {
                                        throw new Error('获取班级作业数据失败');
                                    }
                                    return classResponse.json();
                                })
                                .then(classData => {
                                    if (!classData || !classData.students) {
                                        throw new Error('班级没有作业数据');
                                    }
                                    
                                    // 尝试找到匹配的学生
                                    const studentNames = Object.keys(classData.students);
                                    const matchedName = studentNames.find(name => 
                                        name === student || 
                                        student.includes(name) || 
                                        name.includes(student)
                                    );
                                    
                                    if (matchedName) {
                                        return {
                                            class: className,
                                            student: matchedName,
                                            homework: classData.students[matchedName]
                                        };
                                    }
                                    
                                    throw new Error('未找到学生作业数据');
                                });
                        }
                        return response.json();
                    })
                    .then(data => {
                        console.log(`获取到 ${student} 的作业数据:`, data);
                        
                        if (data.homework && Object.keys(data.homework).length > 0) {
                            renderHomeworkData(contentDiv, data.homework);
                        } else {
                            // 没有作业数据，显示未提交而不是没有作业记录
                            contentDiv.innerHTML = `
                                <div class="text-center p-3">
                                    <i class="bi bi-info-circle text-primary fs-3 mb-3"></i>
                                    <p class="text-muted">该学生未提交作业。</p>
                                    <small class="text-secondary">可能是该学生暂无作业记录或数据同步问题</small>
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('加载作业数据错误:', error);
                        contentDiv.innerHTML = `
                            <div class="text-center p-3">
                                <i class="bi bi-info-circle text-primary fs-3 mb-3"></i>
                                <p class="text-muted">无法加载作业数据</p>
                                <small class="text-secondary">可能是该学生暂无作业记录或数据同步问题</small>
                            </div>
                        `;
                    });
            }
            
            // 新增函数：渲染作业数据
            function renderHomeworkData(contentDiv, homeworkData) {
                // 渲染作业数据
                let homeworkHtml = `
                    <div class="mb-3">
                        <h5>作业成绩列表</h5>
                    </div>
                `;
                
                // 创建作业表格
                homeworkHtml += `
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>作业名称</th>
                                    <th class="text-end">分数</th>
                                </tr>
                            </thead>
                            <tbody>
                `;
                
                // 按作业名称排序
                const sortedHomework = Object.entries(homeworkData)
                    .sort(([a], [b]) => a.localeCompare(b));
                
                let totalScore = 0;
                
                // 添加每个作业
                sortedHomework.forEach(([homework, score]) => {
                    totalScore += score;
                    homeworkHtml += `
                        <tr>
                            <td>${escapeHtml(homework)}</td>
                            <td class="text-end">
                                <span class="badge bg-${getScoreBadgeColor(score)}">${score}</span>
                            </td>
                        </tr>
                    `;
                });
                
                // 计算平均分
                const avgScore = totalScore / sortedHomework.length;
                
                // 添加总结行
                homeworkHtml += `
                    </tbody>
                    <tfoot>
                        <tr class="table-active">
                            <th>平均分</th>
                            <th class="text-end">
                                <span class="badge bg-${getScoreBadgeColor(avgScore)}">${avgScore.toFixed(1)}</span>
                            </th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        `;
                
                contentDiv.innerHTML = homeworkHtml;
            }
            
            // 根据分数获取对应的颜色
            function getScoreBadgeColor(score) {
                if (score >= 90) return 'success';
                if (score >= 75) return 'primary';
                if (score >= 60) return 'warning';
                return 'danger';
            }
            
            // 页面加载后初始化设置问题统计功能
            setupQuestionStatistics();

            // 设置平时成绩下载功能
            setupRegularGradeDownload();

            // 设置平时成绩权重调整功能 - 延迟执行以确保DOM完全加载
            setTimeout(() => {
                setupRegularGradeWeightControl();
            }, 100);

            // 监听平时成绩选项卡的显示事件，确保权重控制功能正常工作
            const regularGradeTab = document.getElementById('regular-grade-tab');
            if (regularGradeTab) {
                regularGradeTab.addEventListener('shown.bs.tab', function() {
                    console.log('平时成绩选项卡已显示，重新初始化权重控制功能');
                    setTimeout(() => {
                        setupRegularGradeWeightControl();
                    }, 100);
                });
            }
        });

        // 设置平时成绩下载功能
        function setupRegularGradeDownload() {
            const downloadBtn = document.getElementById('downloadRegularGradeBtn');
            if (!downloadBtn) return;

            downloadBtn.addEventListener('click', function() {
                if (!window.regularGradeData || window.regularGradeData.length === 0) {
                    alert('没有可下载的平时成绩数据，请先选择班级并加载数据。');
                    return;
                }

                // 获取当前选择的班级名称
                const selectedClass = document.getElementById('classSelector').value;
                const className = selectedClass || '未知班级';

                // 生成CSV内容
                const csvContent = generateRegularGradeCSV(window.regularGradeData);

                // 创建下载链接
                const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
                const link = document.createElement('a');
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);

                // 生成文件名（包含时间戳）
                const now = new Date();
                const timestamp = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}_${String(now.getHours()).padStart(2, '0')}${String(now.getMinutes()).padStart(2, '0')}${String(now.getSeconds()).padStart(2, '0')}`;
                const filename = `${className}_平时成绩_${timestamp}.csv`;
                link.setAttribute('download', filename);

                // 触发下载
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);

                // 显示成功消息
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="bi bi-check-circle"></i> 下载完成';
                this.disabled = true;

                setTimeout(() => {
                    this.innerHTML = originalText;
                    this.disabled = false;
                }, 2000);
            });
        }

        // 生成平时成绩CSV内容
        function generateRegularGradeCSV(data) {
            if (!data || data.length === 0) {
                return '';
            }

            // 获取当前权重设置
            const homeworkWeight = parseFloat(document.getElementById('homeworkWeightSlider').value);

            // CSV头部
            const headers = [
                '排名',
                '学生姓名',
                '作业平均分',
                `作业得分(×${homeworkWeight.toFixed(1)})`,
                '提问记录数',
                '提问得分(最高25)',
                '视频观看数',
                '视频得分(最高25)',
                '平时成绩'
            ];

            // 添加BOM以支持中文
            let csvContent = '\uFEFF';

            // 添加计算参数说明
            csvContent += `平时成绩计算参数\n`;
            csvContent += `作业权重系数,${homeworkWeight.toFixed(1)}\n`;
            csvContent += `计算公式,"平时成绩 = 作业平均分 × ${homeworkWeight.toFixed(1)} + 提问记录分(最高25分) + 视频观看分(最高25分)"\n`;

            // 格式化导出时间，避免CSV解析问题
            const exportTime = new Date();
            const formattedTime = `${exportTime.getFullYear()}-${String(exportTime.getMonth() + 1).padStart(2, '0')}-${String(exportTime.getDate()).padStart(2, '0')} ${String(exportTime.getHours()).padStart(2, '0')}:${String(exportTime.getMinutes()).padStart(2, '0')}:${String(exportTime.getSeconds()).padStart(2, '0')}`;
            csvContent += `导出时间,"${formattedTime}"\n`;
            csvContent += '\n'; // 空行分隔

            // 添加头部
            csvContent += headers.join(',') + '\n';

            // 添加数据行
            data.forEach((item, index) => {
                const row = [
                    index + 1,
                    `"${item.name}"`, // 用引号包围姓名以防止CSV解析问题
                    item.homeworkAvgScore.toFixed(1),
                    item.homeworkScore.toFixed(1),
                    item.chatCount,
                    item.chatScore,
                    item.videoCount,
                    item.videoScore,
                    item.totalRegularGrade.toFixed(1)
                ];
                csvContent += row.join(',') + '\n';
            });

            return csvContent;
        }

        // 设置平时成绩权重调整功能
        function setupRegularGradeWeightControl() {
            const weightSlider = document.getElementById('homeworkWeightSlider');
            const weightValue = document.getElementById('homeworkWeightValue');
            const formulaDisplay = document.getElementById('formulaDisplay');
            const recalculateBtn = document.getElementById('recalculateBtn');

            console.log('setupRegularGradeWeightControl - Elements found:', {
                weightSlider: !!weightSlider,
                weightValue: !!weightValue,
                formulaDisplay: !!formulaDisplay,
                recalculateBtn: !!recalculateBtn
            });

            if (!weightSlider || !weightValue || !formulaDisplay || !recalculateBtn) {
                console.log('setupRegularGradeWeightControl - Some elements not found, returning');
                return;
            }

            // 检查是否已经初始化过，避免重复绑定事件
            if (weightSlider.hasAttribute('data-initialized')) {
                console.log('权重控制功能已经初始化过，跳过重复初始化');
                return;
            }

            // 标记为已初始化
            weightSlider.setAttribute('data-initialized', 'true');
            recalculateBtn.setAttribute('data-initialized', 'true');

            // 权重滑块变化事件
            weightSlider.addEventListener('input', function() {
                const weight = parseFloat(this.value);
                console.log('权重滑块值变化:', weight);

                weightValue.textContent = weight.toFixed(1);

                // 更新公式显示
                formulaDisplay.innerHTML = `
                    平时成绩 = 作业平均分 × <span class="text-primary fw-bold">${weight.toFixed(1)}</span> + 提问记录分（最高25分）+ 视频观看分（最高25分）
                `;

                // 启用重新计算按钮
                recalculateBtn.disabled = false;
                recalculateBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 重新计算';
                recalculateBtn.classList.remove('btn-secondary');
                recalculateBtn.classList.add('btn-success');

                console.log('重新计算按钮已启用');
            });

            // 重新计算按钮点击事件
            recalculateBtn.addEventListener('click', function() {
                console.log('重新计算按钮被点击');
                console.log('当前学生数据:', window.currentStudentData);
                console.log('当前权重值:', weightSlider.value);

                // 检查是否有学生数据
                if (!window.currentStudentData || window.currentStudentData.length === 0) {
                    alert('请先选择班级并加载学生数据');
                    return;
                }

                // 显示计算中状态
                this.disabled = true;
                this.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i> 计算中...';
                this.classList.remove('btn-success');
                this.classList.add('btn-secondary');

                // 重新渲染平时成绩
                try {
                    console.log('开始重新计算平时成绩...');
                    console.log('使用的学生数据:', window.currentStudentData);
                    console.log('当前权重滑块值:', weightSlider.value);

                    // 强制刷新权重显示
                    const currentWeight = parseFloat(weightSlider.value);
                    weightValue.textContent = currentWeight.toFixed(1);
                    formulaDisplay.innerHTML = `
                        平时成绩 = 作业平均分 × <span class="text-primary fw-bold">${currentWeight.toFixed(1)}</span> + 提问记录分（最高25分）+ 视频观看分（最高25分）
                    `;

                    renderRegularGradeSummary(window.currentStudentData);
                    console.log('平时成绩重新计算完成');

                    // 恢复按钮状态
                    this.innerHTML = '<i class="bi bi-check-circle"></i> 已更新';

                    // 显示成功提示
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show mt-2';
                    alertDiv.innerHTML = `
                        <i class="bi bi-check-circle"></i> 平时成绩已使用新的权重系数重新计算完成！
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;

                    // 将提示插入到重新计算按钮的父容器中
                    const buttonContainer = this.closest('.col-md-2');
                    if (buttonContainer) {
                        buttonContainer.appendChild(alertDiv);

                        // 3秒后自动移除提示
                        setTimeout(() => {
                            if (alertDiv.parentNode) {
                                alertDiv.parentNode.removeChild(alertDiv);
                            }
                        }, 3000);
                    }

                    setTimeout(() => {
                        this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 重新计算';
                        this.disabled = false;
                        this.classList.remove('btn-secondary');
                        this.classList.add('btn-success');
                    }, 1500);
                } catch (error) {
                    console.error('重新计算平时成绩时出错:', error);

                    // 显示错误提示
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'alert alert-danger alert-dismissible fade show mt-2';
                    errorDiv.innerHTML = `
                        <i class="bi bi-exclamation-triangle"></i> 重新计算失败：${error.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    `;

                    const buttonContainer = this.closest('.col-md-2');
                    if (buttonContainer) {
                        buttonContainer.appendChild(errorDiv);
                    }

                    // 恢复按钮状态
                    this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 重新计算';
                    this.disabled = false;
                    this.classList.remove('btn-secondary');
                    this.classList.add('btn-success');
                }
            });
        }

        // 添加CSS动画样式
        const style = document.createElement('style');
        style.textContent = `
            .spin {
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }

            .form-range::-webkit-slider-thumb {
                background: #0d6efd;
                border: 2px solid #fff;
                box-shadow: 0 0 0 1px #0d6efd;
            }

            .form-range::-moz-range-thumb {
                background: #0d6efd;
                border: 2px solid #fff;
                box-shadow: 0 0 0 1px #0d6efd;
            }

            .table th {
                border-top: none;
                font-weight: 600;
                font-size: 0.9rem;
            }

            .badge {
                font-size: 0.8rem;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>