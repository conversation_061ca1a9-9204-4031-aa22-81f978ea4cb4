<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权重控制测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>权重控制功能测试</h2>
        <div class="alert alert-info">
            <strong>修复说明：</strong> 已将 renderRegularGradeSummary 函数移动到全局作用域，解决了 "ReferenceError: renderRegularGradeSummary is not defined" 错误。
        </div>
        
        <!-- 计算参数设置卡片 -->
        <div class="card mb-3">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="bi bi-gear-fill"></i> 平时成绩计算参数设置
                </h6>
            </div>
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        <label for="homeworkWeightSlider" class="form-label">
                            <strong>作业权重系数：</strong>
                            <span id="homeworkWeightValue" class="badge bg-primary">0.5</span>
                        </label>
                        <input type="range" class="form-range" id="homeworkWeightSlider"
                               min="0" max="1" step="0.1" value="0.5">
                        <div class="form-text">调整作业平均分在平时成绩中的权重（0-1之间）</div>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-light border mb-0">
                            <i class="bi bi-calculator"></i>
                            <strong>当前计算公式：</strong><br>
                            <span id="formulaDisplay">
                                平时成绩 = 作业平均分 × <span class="text-primary fw-bold">0.5</span> + 提问记录分（最高25分）+ 视频观看分（最高25分）
                            </span>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <button id="recalculateBtn" class="btn btn-success btn-sm w-100">
                            <i class="bi bi-arrow-clockwise"></i> 重新计算
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试结果显示区域 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">测试结果</h6>
            </div>
            <div class="card-body">
                <div id="testResults">
                    <p>请调整权重滑块并点击重新计算按钮进行测试。</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 模拟学生数据
        window.currentStudentData = [
            {
                name: "张三",
                user: "zhangsan",
                chat_views: [{}, {}, {}], // 3次提问
                video_views: [{}, {}, {}, {}, {}], // 5次视频观看
                homework_avg_score: 85.5
            },
            {
                name: "李四",
                user: "lisi",
                chat_views: [{}, {}], // 2次提问
                video_views: [{}], // 1次视频观看
                homework_avg_score: 92.0
            },
            {
                name: "王五",
                user: "wangwu",
                chat_views: [], // 0次提问
                video_views: [{}, {}, {}], // 3次视频观看
                homework_avg_score: 78.3
            }
        ];

        // 模拟 renderRegularGradeSummary 函数
        function renderRegularGradeSummary(students) {
            console.log('renderRegularGradeSummary called with students:', students);
            
            const homeworkWeightSlider = document.getElementById('homeworkWeightSlider');
            const homeworkWeight = homeworkWeightSlider ? parseFloat(homeworkWeightSlider.value) : 0.5;
            
            console.log('当前作业权重:', homeworkWeight);
            
            let html = '<h5>计算结果：</h5><table class="table table-striped"><thead><tr><th>学生</th><th>作业平均分</th><th>作业得分</th><th>提问得分</th><th>视频得分</th><th>总分</th></tr></thead><tbody>';
            
            students.forEach(student => {
                const displayName = student.name || student.user;
                const chatCount = student.chat_views ? student.chat_views.length : 0;
                const videoCount = student.video_views ? student.video_views.length : 0;
                const homeworkAvgScore = student.homework_avg_score ? parseFloat(student.homework_avg_score) : 0;
                
                const homeworkScore = homeworkAvgScore * homeworkWeight;
                const chatScore = Math.min(chatCount, 25);
                const videoScore = Math.min(videoCount, 25);
                const totalScore = homeworkScore + chatScore + videoScore;
                
                html += `<tr>
                    <td>${displayName}</td>
                    <td>${homeworkAvgScore.toFixed(1)}</td>
                    <td>${homeworkScore.toFixed(1)}</td>
                    <td>${chatScore}</td>
                    <td>${videoScore}</td>
                    <td><strong>${totalScore.toFixed(1)}</strong></td>
                </tr>`;
            });
            
            html += '</tbody></table>';
            
            document.getElementById('testResults').innerHTML = html;
            console.log('测试结果已更新');
        }

        // 设置权重控制功能
        function setupRegularGradeWeightControl() {
            const weightSlider = document.getElementById('homeworkWeightSlider');
            const weightValue = document.getElementById('homeworkWeightValue');
            const formulaDisplay = document.getElementById('formulaDisplay');
            const recalculateBtn = document.getElementById('recalculateBtn');

            console.log('setupRegularGradeWeightControl - Elements found:', {
                weightSlider: !!weightSlider,
                weightValue: !!weightValue,
                formulaDisplay: !!formulaDisplay,
                recalculateBtn: !!recalculateBtn
            });

            if (!weightSlider || !weightValue || !formulaDisplay || !recalculateBtn) {
                console.log('setupRegularGradeWeightControl - Some elements not found, returning');
                return;
            }

            // 权重滑块变化事件
            weightSlider.addEventListener('input', function() {
                const weight = parseFloat(this.value);
                console.log('权重滑块值变化:', weight);
                
                weightValue.textContent = weight.toFixed(1);

                // 更新公式显示
                formulaDisplay.innerHTML = `
                    平时成绩 = 作业平均分 × <span class="text-primary fw-bold">${weight.toFixed(1)}</span> + 提问记录分（最高25分）+ 视频观看分（最高25分）
                `;

                // 启用重新计算按钮
                recalculateBtn.disabled = false;
                recalculateBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 重新计算';
                recalculateBtn.classList.remove('btn-secondary');
                recalculateBtn.classList.add('btn-success');
                
                console.log('重新计算按钮已启用');
            });

            // 重新计算按钮点击事件
            recalculateBtn.addEventListener('click', function() {
                console.log('重新计算按钮被点击');
                console.log('当前学生数据:', window.currentStudentData);
                console.log('当前权重值:', weightSlider.value);

                // 检查是否有学生数据
                if (!window.currentStudentData || window.currentStudentData.length === 0) {
                    alert('请先选择班级并加载学生数据');
                    return;
                }

                // 显示计算中状态
                this.disabled = true;
                this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 计算中...';
                this.classList.remove('btn-success');
                this.classList.add('btn-secondary');

                // 重新渲染平时成绩
                try {
                    console.log('开始重新计算平时成绩...');
                    renderRegularGradeSummary(window.currentStudentData);
                    console.log('平时成绩重新计算完成');

                    // 恢复按钮状态
                    this.innerHTML = '<i class="bi bi-check-circle"></i> 已更新';
                    
                    setTimeout(() => {
                        this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 重新计算';
                        this.disabled = false;
                        this.classList.remove('btn-secondary');
                        this.classList.add('btn-success');
                    }, 1500);
                } catch (error) {
                    console.error('重新计算平时成绩时出错:', error);
                    
                    // 恢复按钮状态
                    this.innerHTML = '<i class="bi bi-arrow-clockwise"></i> 重新计算';
                    this.disabled = false;
                    this.classList.remove('btn-secondary');
                    this.classList.add('btn-success');
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成，初始化权重控制功能');
            setupRegularGradeWeightControl();
            
            // 初始渲染
            renderRegularGradeSummary(window.currentStudentData);
        });
    </script>
</body>
</html>
